'use client'

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import LightBrowser from './LightBrowser'
import OutlineCard from './OutlineCard'

interface PureContentViewerProps {
  activeTab: {
    id: string
    title: string
    sourceType: 'url' | 'text'
    sourceData: string
    originalContent: string
    aiNoteMarkdown: string
    isLoading: boolean
    aiAnalyzing: boolean
  }
}

const PureContentViewer: React.FC<PureContentViewerProps> = ({ activeTab }) => {
  const [outline, setOutline] = useState<Array<{id: string, title: string, level: number}>>([])
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  // 流式生成智能大纲
  const generateSmartOutlineStream = async (content: string, title: string) => {
    setIsGeneratingOutline(true)
    setOutline([]) // 清空现有大纲

    try {
      const response = await fetch('/api/generate-outline/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, title }),
      })

      if (!response.ok) {
        throw new Error('流式大纲生成请求失败')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'outline_update' && data.outline) {
                setOutline(data.outline)
              } else if (data.type === 'outline_complete' && data.outline) {
                setOutline(data.outline)
                break
              } else if (data.type === 'error') {
                console.error('流式大纲生成错误:', data.error)
                generateFallbackOutline(content)
                return
              }
            } catch (parseError) {
              console.warn('解析流式数据失败:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('流式生成智能大纲失败:', error)
      generateFallbackOutline(content)
    } finally {
      setIsGeneratingOutline(false)
    }
  }

  // 非流式生成智能大纲（备用方案）
  const generateSmartOutline = async (content: string, title: string) => {
    setIsGeneratingOutline(true)
    try {
      const response = await fetch('/api/generate-outline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, title }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.outline && Array.isArray(data.outline)) {
          setOutline(data.outline)
        }
      } else {
        console.warn('LLM大纲生成失败，使用备用方案')
        generateFallbackOutline(content)
      }
    } catch (error) {
      console.error('生成智能大纲失败:', error)
      generateFallbackOutline(content)
    } finally {
      setIsGeneratingOutline(false)
    }
  }

  // 备用大纲生成（基于简单文本解析）
  const generateFallbackOutline = (content: string) => {
    const lines = content.split('\n')
    const headings: Array<{id: string, title: string, level: number}> = []

    lines.forEach((line, index) => {
      // 检测Markdown标题
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headingMatch) {
        const level = headingMatch[1].length
        const title = headingMatch[2].trim()
        const id = title.toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .substring(0, 50)
        headings.push({ id: id || `heading-${index}`, title, level })
      }
      // 检测其他可能的标题格式
      else if (line.trim() && !line.startsWith(' ') && line.length < 100 && line.length > 3) {
        const nextLine = lines[index + 1]
        if (nextLine && (nextLine.startsWith('=') || nextLine.startsWith('-'))) {
          const title = line.trim()
          const id = title.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 50)
          headings.push({ id: id || `heading-${index}`, title, level: nextLine.startsWith('=') ? 1 : 2 })
        }
      }
    })
    setOutline(headings)
  }

  // 生成内容大纲
  useEffect(() => {
    if (!activeTab.originalContent && !activeTab.sourceData) return

    const content = activeTab.originalContent || activeTab.sourceData
    const title = activeTab.title || '未命名文档'

    // 优先使用流式LLM生成智能大纲
    if (content.length > 100) { // 只对有足够内容的文档生成智能大纲
      generateSmartOutlineStream(content, title)
    } else {
      generateFallbackOutline(content)
    }
  }, [activeTab.originalContent, activeTab.sourceData, activeTab.title])

  // 滚动到指定标题
  const scrollToHeading = (headingId: string) => {
    const element = document.getElementById(headingId)
    if (element) {
      // 获取主内容区域的滚动容器
      const scrollContainer = document.querySelector('.overflow-y-auto')
      if (scrollContainer) {
        // 计算元素相对于滚动容器的位置
        const containerRect = scrollContainer.getBoundingClientRect()
        const elementRect = element.getBoundingClientRect()
        const scrollTop = scrollContainer.scrollTop
        const targetScrollTop = scrollTop + elementRect.top - containerRect.top - 100 // 100px 的顶部偏移

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        })
      } else {
        // 备用方案：使用默认的 scrollIntoView
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
  }

  // 渲染内容
  const renderContent = () => {
    if (activeTab.sourceType === 'url') {
      return (
        <div className="w-full">
          <div className="rounded-2xl overflow-hidden shadow-lg border border-gray-200/50">
            <LightBrowser
              url={activeTab.sourceData}
              title={activeTab.title}
              onLoadComplete={() => {}}
              onError={() => {}}
              className="h-[800px]"
            />
          </div>
        </div>
      )
    } else {
      return (
        <div className="w-full">
          <div className="py-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-8 border-b border-gray-200 pb-4">
              {activeTab.title}
            </h1>
            <div
              ref={contentRef}
              className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-6 prose-ul:my-4 prose-ol:my-4 prose-li:my-2 prose-li:leading-relaxed prose-blockquote:border-l-4 prose-blockquote:border-blue-400 prose-blockquote:bg-blue-50/50 prose-blockquote:pl-6 prose-blockquote:py-4 prose-blockquote:my-6 prose-blockquote:rounded-r-lg prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto prose-pre:my-6 prose-strong:text-gray-900 prose-strong:font-semibold prose-em:text-gray-800 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline"
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                lineHeight: '1.8'
              }}
            >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h1
                      id={id}
                      className="text-3xl font-bold text-gray-900 mb-6 mt-8 pb-3 border-b-2 border-gray-200"
                      {...props}
                    >
                      {children}
                    </h1>
                  )
                },
                h2: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h2
                      id={id}
                      className="text-2xl font-semibold text-gray-900 mb-4 mt-6 pb-2 border-b border-gray-200"
                      {...props}
                    >
                      {children}
                    </h2>
                  )
                },
                h3: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h3
                      id={id}
                      className="text-xl font-semibold text-gray-900 mb-3 mt-5"
                      {...props}
                    >
                      {children}
                    </h3>
                  )
                },
                h4: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h4
                      id={id}
                      className="text-lg font-medium text-gray-900 mb-2 mt-4"
                      {...props}
                    >
                      {children}
                    </h4>
                  )
                },
                h5: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h5
                      id={id}
                      className="text-base font-medium text-gray-900 mb-2 mt-3"
                      {...props}
                    >
                      {children}
                    </h5>
                  )
                },
                h6: ({ children, ...props }) => {
                  const text = typeof children === 'string' ? children : String(children)
                  const id = text.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 50)
                  return (
                    <h6
                      id={id}
                      className="text-sm font-medium text-gray-900 mb-2 mt-3"
                      {...props}
                    >
                      {children}
                    </h6>
                  )
                },
                p: ({ children }) => (
                  <p className="text-gray-700 leading-relaxed mb-6 text-base" style={{ whiteSpace: 'pre-wrap', fontSize: '16px' }}>
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-outside ml-6 mb-6 space-y-2 text-gray-700 text-base" style={{ fontSize: '16px' }}>
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-outside ml-6 mb-6 space-y-2 text-gray-700 text-base" style={{ fontSize: '16px' }}>
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="leading-relaxed text-base" style={{ fontSize: '16px' }}>
                    {children}
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-400 bg-blue-50/30 pl-4 py-2 my-4 rounded-r-lg text-base" style={{ fontSize: '16px' }}>
                    {children}
                  </blockquote>
                ),
                code: ({ children, className }) => {
                  const isInline = !className?.includes('language-')
                  if (isInline) {
                    return (
                      <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">
                        {children}
                      </code>
                    )
                  }
                  return (
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4">
                      <code className="text-sm font-mono">{children}</code>
                    </pre>
                  )
                }
              }}
            >
              {activeTab.originalContent || activeTab.sourceData}
            </ReactMarkdown>
            </div>
          </div>
        </div>
      )
    }
  }

  return (
    <div className="h-full flex relative bg-gray-50/30">
      {/* 左侧大纲卡片 - 始终显示，无需编辑模式 */}
      {(outline.length > 0 || isGeneratingOutline) && (
        <div className="w-80 p-6 flex-shrink-0">
          <OutlineCard
            outline={outline}
            onHeadingClick={scrollToHeading}
            isLoading={isGeneratingOutline}
          />
        </div>
      )}

      {/* 主内容区域 - 限制最大宽度，提供更好的阅读体验 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        <div className="max-w-4xl mx-auto px-8 py-12">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default PureContentViewer
