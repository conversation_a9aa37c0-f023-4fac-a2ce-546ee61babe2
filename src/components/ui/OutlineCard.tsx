'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { List } from 'lucide-react'

interface OutlineItem {
  id: string
  title: string
  level: number
}

interface OutlineCardProps {
  outline: OutlineItem[]
  onHeadingClick: (headingId: string) => void
  isLoading?: boolean
}

const OutlineCard: React.FC<OutlineCardProps> = ({ outline, onHeadingClick, isLoading = false }) => {
  const [activeHeading, setActiveHeading] = useState<string | null>(null)

  const handleHeadingClick = (headingId: string) => {
    setActiveHeading(headingId)
    onHeadingClick(headingId)
  }

  const getIndentClass = (level: number) => {
    switch (level) {
      case 1: return 'pl-0'
      case 2: return 'pl-4'
      case 3: return 'pl-8'
      case 4: return 'pl-12'
      case 5: return 'pl-16'
      case 6: return 'pl-20'
      default: return 'pl-0'
    }
  }

  const getFontSizeClass = (level: number) => {
    switch (level) {
      case 1: return 'text-base font-semibold'
      case 2: return 'text-sm font-medium'
      case 3: return 'text-sm'
      case 4: return 'text-xs'
      case 5: return 'text-xs'
      case 6: return 'text-xs'
      default: return 'text-sm'
    }
  }

  if (outline.length === 0 && !isLoading) return null

  return (
    <div className="sticky top-6 h-[calc(100vh-3rem)]">
      {/* 毛玻璃效果的大圆角卡片 */}
      <div className="backdrop-blur-xl bg-white/90 border border-gray-200/30 rounded-3xl shadow-2xl overflow-hidden ring-1 ring-white/20 h-full flex flex-col">


        {/* 大纲内容 */}
        <div className="p-4 overflow-y-auto flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm text-gray-600">正在生成智能大纲...</span>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                {outline.map((item, index) => (
                  <button
                    key={`${item.id}-${index}`}
                    onClick={() => handleHeadingClick(item.id)}
                    className={cn(
                      "w-full text-left py-2 px-3 rounded-lg transition-all duration-200 hover:bg-blue-50/50 group",
                      getIndentClass(item.level),
                      activeHeading === item.id
                        ? "bg-blue-100/50 text-blue-700 border-l-2 border-blue-500"
                        : "text-gray-700 hover:text-gray-900"
                    )}
                  >
                    <div className={cn(
                      "truncate",
                      getFontSizeClass(item.level)
                    )}>
                      {item.title}
                    </div>
                    {item.level === 1 && (
                      <div className="w-full h-px bg-gray-200/50 mt-1"></div>
                    )}
                  </button>
                ))}
              </div>
            )}
        </div>

        {/* 卡片底部装饰 */}
        <div className="h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500"></div>
      </div>
    </div>
  )
}

export default OutlineCard
